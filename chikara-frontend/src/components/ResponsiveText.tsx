import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface ResponsiveTextProps {
    children: ReactNode;
    className?: string;
    size?: "xs" | "sm" | "base" | "lg" | "xl";
}

/**
 * ResponsiveText component that automatically adjusts text size for smaller screens
 * while maintaining readability and proper spacing
 */
export const ResponsiveText = ({ children, className, size = "base" }: ResponsiveTextProps) => {
    const sizeClasses = {
        xs: "text-[10px] xs:text-xs",
        sm: "text-xs xs:text-sm",
        base: "text-sm xs:text-base",
        lg: "text-base xs:text-lg",
        xl: "text-lg xs:text-xl",
    };

    return <span className={cn(sizeClasses[size], className)}>{children}</span>;
};

interface ResponsiveContainerProps {
    children: ReactNode;
    className?: string;
    padding?: "none" | "xs" | "sm" | "md" | "lg";
}

/**
 * ResponsiveContainer component that adjusts padding and spacing for different screen sizes
 */
export const ResponsiveContainer = ({ children, className, padding = "md" }: ResponsiveContainerProps) => {
    const paddingClasses = {
        none: "",
        xs: "p-1 xs:p-1.5",
        sm: "p-1.5 xs:p-2",
        md: "p-2 xs:p-3 sm:p-4",
        lg: "p-3 xs:p-4 sm:p-6",
    };

    return <div className={cn(paddingClasses[padding], className)}>{children}</div>;
};

interface ResponsiveGridProps {
    children: ReactNode;
    className?: string;
    cols?: 1 | 2 | 3 | 4 | 5 | 6;
    gap?: "xs" | "sm" | "md" | "lg";
}

/**
 * ResponsiveGrid component that adjusts grid layout for different screen sizes
 */
export const ResponsiveGrid = ({ children, className, cols = 2, gap = "md" }: ResponsiveGridProps) => {
    const gridClasses = {
        1: "grid-cols-1",
        2: "grid-cols-1 xs:grid-cols-2",
        3: "grid-cols-1 xs:grid-cols-2 sm:grid-cols-3",
        4: "grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4",
        5: "grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 md:grid-cols-5",
        6: "grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 md:grid-cols-6",
    };

    const gapClasses = {
        xs: "gap-1 xs:gap-1.5",
        sm: "gap-1.5 xs:gap-2",
        md: "gap-2 xs:gap-3",
        lg: "gap-3 xs:gap-4",
    };

    return <div className={cn("grid", gridClasses[cols], gapClasses[gap], className)}>{children}</div>;
};
