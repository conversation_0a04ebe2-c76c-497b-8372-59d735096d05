import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface SidebarToggleProps {
    isCollapsed: boolean;
    onToggle: () => void;
}

export default function SidebarToggle({ isCollapsed, onToggle }: SidebarToggleProps) {
    return (
        <button
            onClick={onToggle}
            className={cn(
                "flex h-6 w-6 items-center justify-center rounded-md",
                "bg-gray-700/80 border border-gray-600/50 text-gray-300 shadow-sm",
                "hover:bg-gray-600 hover:text-white transition-colors",
                "focus:outline-none focus:ring-1 focus:ring-blue-500"
            )}
            title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
            {isCollapsed ? <ChevronRight className="h-3 w-3" /> : <ChevronLeft className="h-3 w-3" />}
        </button>
    );
}
