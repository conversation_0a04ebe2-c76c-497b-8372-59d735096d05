import DiscordIcon from "@/assets/icons/logos/DiscordIcon";
import Spinner from "@/components/Spinners/Spinner";
import useGetAvailableQuestList from "@/features/tasks/api/useGetAvailableQuestList";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { UTCDateMini } from "@date-fns/utc";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { usePersistStore } from "../../../app/store/stores";
import DevMenu from "../../../features/dev/components/DevMenu";
import SideBarNavigation from "./SideBarNavigation";
import SideBarNavigationCollapsed from "./SideBarNavigationCollapsed";
import SidebarProfile from "./SidebarProfile";
import SidebarProfileCollapsed from "./SidebarProfileCollapsed";
import SidebarToggle from "./SidebarToggle";

export default function SideBar() {
    const [clockFormat12HR, setClockFormat12HR] = useState<boolean>(false);
    const isDev = import.meta.env.MODE === "development";
    const { data: currentUser, isLoading, error } = useFetchCurrentUser();
    const { data: availableQuests } = useGetAvailableQuestList();
    const { twelveHrClock, sidebarCollapsed, setSidebarCollapsed } = usePersistStore();

    const [showDevMenu, setShowDevMenu] = useState<boolean>(false);
    useEffect(() => {
        setClockFormat12HR(twelveHrClock);
    }, [twelveHrClock]);

    if (error && !error?.message) {
        console.error("User fetch error:", error);
        throw new Error("Unable to connect to server. Please check your connection and try again.");
    }

    const openDevMenu = (): void => {
        if (!isDev) return;
        if (showDevMenu) {
            setShowDevMenu(false);
        } else {
            setShowDevMenu(true);
        }
    };

    const iconSize = "2xl:h-6 2xl:w-6 h-5 w-5";

    const toggleSidebar = () => {
        setSidebarCollapsed(!sidebarCollapsed);
    };

    return (
        <div
            className={cn(
                "sidebar_shadow hidden overflow-hidden bg-linear-to-t bg-white md:block dark:bg-[#171925] flex-shrink-0 transition-all duration-300",
                sidebarCollapsed ? "w-20" : "w-48 xl:w-60 2xl:w-72"
            )}
        >
            <div className="relative mt-2 flex size-full flex-col min-h-0">
                {isLoading || error ? (
                    <Spinner className="mx-auto" />
                ) : sidebarCollapsed ? (
                    <SidebarProfileCollapsed currentUser={currentUser} />
                ) : (
                    <SidebarProfile currentUser={currentUser} />
                )}

                {sidebarCollapsed ? (
                    <SideBarNavigationCollapsed currentUser={currentUser} availableQuests={availableQuests} />
                ) : (
                    <SideBarNavigation currentUser={currentUser} availableQuests={availableQuests} />
                )}

                <div
                    data-testid="sidebar-footer"
                    className={cn(
                        "mt-auto border-t border-gray-500/20 bg-gray-900/50 backdrop-blur-sm",
                        sidebarCollapsed ? "px-2 py-2" : "px-4 py-3"
                    )}
                >
                    {sidebarCollapsed ? (
                        <div className="flex flex-col items-center gap-2">
                            <div className="flex  items-center gap-1">
                                <Link to="discord" className="group relative">
                                    <button className="flex h-7 w-7 items-center justify-center rounded-md bg-indigo-600/80 text-indigo-200 shadow-sm ring-1 ring-white/10 transition-all hover:bg-indigo-500 hover:text-white hover:ring-white/20">
                                        <span className="sr-only">Join Discord</span>
                                        <DiscordIcon className="h-3 w-3" aria-hidden="true" />
                                    </button>
                                    <span className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-gray-300 opacity-0 transition-opacity group-hover:opacity-100">
                                        Discord
                                    </span>
                                </Link>

                                <a
                                    href="https://chikaraacademymmo.wiki.gg/"
                                    target="_blank"
                                    rel="noreferrer"
                                    className="group relative"
                                >
                                    <button className="flex h-7 w-7 items-center justify-center rounded-md bg-blue-600/80 text-blue-200 shadow-sm ring-1 ring-white/10 transition-all hover:bg-blue-500 hover:text-white hover:ring-white/20">
                                        <span className="sr-only">Wiki</span>
                                        <img
                                            src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/MhJCUZW.png`}
                                            className="h-3 w-3"
                                            alt="Wiki"
                                        />
                                    </button>
                                    <span className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-gray-300 opacity-0 transition-opacity group-hover:opacity-100">
                                        Wiki
                                    </span>
                                </a>
                            </div>

                            <div className="border-t border-gray-600/30 pt-2 w-full flex gap-2 justify-center items-center">
                                <p
                                    className={cn(
                                        "text-xs text-gray-400",
                                        isDev && "cursor-pointer hover:text-purple-400"
                                    )}
                                    onClick={() => openDevMenu()}
                                >
                                    {import.meta.env.VITE_PACKAGE_VERSION}
                                </p>
                                <SidebarToggle isCollapsed={sidebarCollapsed} onToggle={toggleSidebar} />
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-2">
                            <div className="flex items-center justify-between">
                                <div className="flex gap-3">
                                    <Link to="discord" className="group relative">
                                        <button className="flex h-9 w-9 items-center justify-center rounded-lg bg-indigo-600/80 text-indigo-200 shadow-lg ring-1 ring-white/10 transition-all hover:bg-indigo-500 hover:text-white hover:ring-white/20 hover:shadow-indigo-500/25">
                                            <span className="sr-only">Join Discord</span>
                                            <DiscordIcon className={iconSize} aria-hidden="true" />
                                        </button>
                                        <span className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-gray-300 opacity-0 transition-opacity group-hover:opacity-100">
                                            Discord
                                        </span>
                                    </Link>

                                    <a
                                        href="https://chikaraacademymmo.wiki.gg/"
                                        target="_blank"
                                        rel="noreferrer"
                                        className="group relative"
                                    >
                                        <button className="flex h-9 w-9 items-center justify-center rounded-lg bg-blue-600/80 text-blue-200 shadow-lg ring-1 ring-white/10 transition-all hover:bg-blue-500 hover:text-white hover:ring-white/20 hover:shadow-blue-500/25">
                                            <span className="sr-only">Wiki</span>
                                            <img
                                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/MhJCUZW.png`}
                                                className="h-4 w-4"
                                                alt="Wiki"
                                            />
                                        </button>
                                        <span className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-gray-300 opacity-0 transition-opacity group-hover:opacity-100">
                                            Wiki
                                        </span>
                                    </a>
                                </div>

                                <div className="flex flex-col items-end gap-1">
                                    <div
                                        className={cn(
                                            "text-sm font-medium text-blue-300 dark:text-blue-400",
                                            isDev && "cursor-pointer hover:text-blue-200"
                                        )}
                                        onClick={() => openDevMenu()}
                                    >
                                        {clockFormat12HR
                                            ? format(new UTCDateMini(), "h:mm a")
                                            : format(new UTCDateMini(), "H:mm")}
                                    </div>
                                    <p
                                        className={cn(
                                            "text-xs text-gray-400",
                                            isDev && "cursor-pointer hover:text-purple-400"
                                        )}
                                        onClick={() => openDevMenu()}
                                    >
                                        v{import.meta.env.VITE_PACKAGE_VERSION}
                                    </p>
                                </div>
                            </div>

                            <div className="border-t border-gray-500/20 pt-2 flex justify-center">
                                <SidebarToggle isCollapsed={sidebarCollapsed} onToggle={toggleSidebar} />
                            </div>
                        </div>
                    )}
                </div>

                {showDevMenu && <DevMenu setShowDevMenu={setShowDevMenu} />}
            </div>
        </div>
    );
}
