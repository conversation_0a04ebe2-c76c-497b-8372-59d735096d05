import { DisplayAvatar } from "@/components/DisplayAvatar";
import { Link } from "react-router-dom";
import type { User } from "@/types/user";

interface SidebarProfileCollapsedProps {
    currentUser?: User | undefined;
}

export default function SidebarProfileCollapsed({ currentUser }: SidebarProfileCollapsedProps) {
    return (
        <section className="relative mx-2 select-none rounded-md border pt-2 pb-2 shadow-2xl dark:border-gray-800 dark:bg-gray-900">
            <div className="flex flex-col items-center">
                <Link className="flex" to={`/profile/${currentUser?.id}`}>
                    <div className="w-12 h-12 shadow-sm drop-shadow-lg">
                        <DisplayAvatar
                            src={currentUser}
                            className={`mx-auto h-12 w-12 rounded-lg border-2 border-black object-cover dark:border-gray-700 ${
                                currentUser?.hospitalisedUntil !== null ? `grayscale` : ``
                            }`}
                        />
                    </div>
                </Link>
            </div>
        </section>
    );
}
