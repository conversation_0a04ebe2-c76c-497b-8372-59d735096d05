import { hospitalisedNavItems, jailedNavItems, navItems } from "@/helpers/navItems";
import { cn } from "@/lib/utils";
import { Fragment } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { useNormalStore } from "../../../app/store/stores";
import NotificationBadge from "@/components/NotificationBadge";
import type { User } from "@/types/user";
import type { QuestWithProgress } from "@/types/quest";

interface NavItem {
    name: string;
    href: string;
    icon: string;
    current?: string;
    external?: boolean;
    thin?: boolean;
    construction?: boolean;
}

interface SideBarNavigationCollapsedProps {
    availableQuests?: QuestWithProgress[] | undefined;
    currentUser?: User | undefined;
}

interface CollapsedNavItemProps {
    item: NavItem;
    inFight?: string | null;
    availableQuests?: number;
    craftCollectReady?: boolean;
}

const CollapsedNavItem = ({ item, inFight, availableQuests, craftCollectReady }: CollapsedNavItemProps) => {
    const location = useLocation();

    const checkCurrent = (name?: string): boolean => {
        if (!name) return false;
        return `/${name}` === location.pathname;
    };

    if (item.external) {
        return (
            <a
                href={item.href}
                target="_blank"
                rel="noreferrer"
                className={cn(
                    "group relative flex h-12 w-12 items-center justify-center rounded-lg transition-all hover:brightness-110",
                    "roundedBtnBlue"
                )}
                title={item.name}
            >
                <img
                    src={item.icon}
                    alt={item.name}
                    className={cn(
                        "h-8 w-8 group-hover:scale-105 transition-transform",
                        (item.construction || inFight) && "brightness-50"
                    )}
                />
            </a>
        );
    }

    return (
        <NavLink
            to={!inFight ? item.href : "#"}
            aria-current={checkCurrent(item.current) ? "page" : undefined}
            className={cn(
                checkCurrent(item.current) ? "roundedBtnLightBlue" : "roundedBtnBlue",
                "group relative flex h-12 w-12 items-center justify-center rounded-lg transition-all hover:brightness-110"
            )}
            title={item.name}
        >
            {/* Notification badges */}
            <div className="absolute inset-0 pointer-events-none">
                {item.name === "Campus" && craftCollectReady && (
                    <NotificationBadge empty pulse className="absolute -top-1 -right-1 size-4" />
                )}

                {item.name === "Tasks" && availableQuests ? (
                    <NotificationBadge amount={availableQuests} className="absolute -top-1 -right-1 size-4 text-xs" />
                ) : null}
            </div>

            <img
                src={item.icon}
                alt={item.name}
                className={cn(
                    "h-8 w-8 group-hover:scale-105 transition-transform",
                    (item.construction || inFight) && "brightness-50"
                )}
            />
        </NavLink>
    );
};

export default function SideBarNavigationCollapsed({ availableQuests, currentUser }: SideBarNavigationCollapsedProps) {
    const hospitalised = (currentUser?.hospitalisedUntil ?? 0) > 0;
    const jailed = (currentUser?.jailedUntil ?? 0) > 0;
    const inFight = currentUser?.battleValidUntil;
    const { preventNavigation, craftCollectReady } = useNormalStore();
    let navigation: ReturnType<typeof navItems> = [];

    if (!hospitalised && !jailed) {
        navigation = navItems(currentUser?.userType ?? null);
    } else if (hospitalised) {
        navigation = hospitalisedNavItems(currentUser?.userType ?? null);
    } else {
        navigation = jailedNavItems(currentUser?.userType ?? null);
    }

    if (preventNavigation) {
        return (
            <div className="flex flex-1 flex-col px-2 pt-2 min-h-0">
                <div className="flex flex-col gap-2 items-center"></div>
            </div>
        );
    }

    return (
        <div className="flex flex-1 flex-col px-2 pt-2 min-h-0">
            <div className="flex flex-col gap-2 items-center overflow-y-auto">
                {navigation.map((item, index) => (
                    <Fragment key={index}>
                        <CollapsedNavItem
                            item={item}
                            inFight={inFight?.toString() ?? null}
                            availableQuests={availableQuests?.length}
                            craftCollectReady={craftCollectReady}
                        />
                    </Fragment>
                ))}
            </div>
        </div>
    );
}
