import React, { Component, ErrorInfo, ReactNode } from "react";
import TechnicalError from "@/components/TechnicalError";

interface ErrorBoundaryProps {
    children: ReactNode;
    fallback?: ReactNode;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
    showDetails?: boolean;
    enableRetry?: boolean;
    enableReporting?: boolean;
    level?: "page" | "component" | "critical";
}

interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
    errorInfo?: ErrorInfo;
    retryCount: number;
    errorId: string;
}

interface ErrorCategory {
    type: "chunk" | "network" | "runtime" | "unknown";
    severity: "low" | "medium" | "high" | "critical";
    recoverable: boolean;
    autoRetry: boolean;
}

/**
 * Categorizes errors for better handling and user experience
 */
function categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || "";

    // Chunk loading errors (usually recoverable with refresh)
    if (
        message.includes("loading chunk") ||
        message.includes("dynamically imported module") ||
        message.includes("importing a module script failed")
    ) {
        return {
            type: "chunk",
            severity: "medium",
            recoverable: true,
            autoRetry: true,
        };
    }

    // Network errors
    if (message.includes("network") || message.includes("fetch") || message.includes("timeout")) {
        return {
            type: "network",
            severity: "medium",
            recoverable: true,
            autoRetry: false,
        };
    }

    // Runtime errors
    if (
        message.includes("cannot read property") ||
        message.includes("undefined is not a function") ||
        stack.includes("typeerror")
    ) {
        return {
            type: "runtime",
            severity: "high",
            recoverable: false,
            autoRetry: false,
        };
    }

    return {
        type: "unknown",
        severity: "high",
        recoverable: false,
        autoRetry: false,
    };
}

/**
 * Generates a unique error ID for tracking and reporting
 */
function generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Enhanced Error Boundary with better error categorization, retry functionality,
 * and user-friendly error displays
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    private retryTimeoutId: NodeJS.Timeout | null = null;

    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = {
            hasError: false,
            retryCount: 0,
            errorId: "",
        };
    }

    static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
        return {
            hasError: true,
            error,
            errorId: generateErrorId(),
        };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        this.setState({ errorInfo });

        // Log error
        console.error("Error caught by Error Boundary:", {
            error,
            errorInfo,
            errorId: this.state.errorId,
        });

        // Call custom error handler
        this.props.onError?.(error, errorInfo);

        // Report error if enabled
        if (this.props.enableReporting) {
            this.reportError(error, errorInfo);
        }

        // Auto-retry for certain error types
        const category = categorizeError(error);
        if (category.autoRetry && this.state.retryCount < 3) {
            this.scheduleRetry(category.type === "chunk" ? 1000 : 3000);
        }
    }

    componentWillUnmount(): void {
        if (this.retryTimeoutId) {
            clearTimeout(this.retryTimeoutId);
        }
    }

    private scheduleRetry = (delay: number): void => {
        this.retryTimeoutId = setTimeout(() => {
            this.handleRetry();
        }, delay);
    };

    private handleRetry = (): void => {
        this.setState((prevState) => ({
            hasError: false,
            error: undefined,
            errorInfo: undefined,
            retryCount: prevState.retryCount + 1,
        }));
    };

    private reportError = (error: Error, errorInfo: ErrorInfo): void => {
        // Here you would integrate with your error reporting service
        // For example: Sentry, LogRocket, etc.
        console.log("Reporting error:", {
            error: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            errorId: this.state.errorId,
        });
    };

    render(): ReactNode {
        if (this.state.hasError) {
            return (
                this.props.fallback || (
                    <TechnicalError
                        error={this.state.error}
                        errorInfo={this.state.errorInfo}
                        errorId={this.state.errorId}
                        retryCount={this.state.retryCount}
                        onRetry={this.handleRetry}
                        showDetails={this.props.showDetails}
                        enableRetry={this.props.enableRetry}
                        level={this.props.level}
                        boundary
                    />
                )
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;
